import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';

/// خدمة إدارة البيانات المحفوظة محلياً
class SharedPreferencesService {
  static SharedPreferences? _prefs;

  // Keys
  static const String _keyIsFirstTime = 'is_first_time';
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserId = 'user_id';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserName = 'user_name';
  static const String _keyUserPhone = 'user_phone';
  static const String _keyAccessToken = 'access_token';
  static const String _keyRefreshToken = 'refresh_token';

  /// تهيئة الخدمة
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// الحصول على instance من SharedPreferences
  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception(
        'SharedPreferencesService not initialized. Call init() first.',
      );
    }
    return _prefs!;
  }

  // ==================== First Time ====================

  /// التحقق من أول مرة تشغيل
  Future<bool> isFirstTime() async {
    return prefs.getBool(_keyIsFirstTime) ?? true;
  }

  /// تعيين أن التطبيق تم تشغيله من قبل
  Future<void> setNotFirstTime() async {
    await prefs.setBool(_keyIsFirstTime, false);
  }

  // ==================== Login State ====================

  /// التحقق من حالة تسجيل الدخول
  Future<bool> isLoggedIn() async {
    return prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  /// تعيين حالة تسجيل الدخول
  Future<void> setLoggedIn(bool isLoggedIn) async {
    await prefs.setBool(_keyIsLoggedIn, isLoggedIn);
  }

  // ==================== User Data ====================

  /// حفظ بيانات المستخدم
  Future<void> saveUserData({
    required String userId,
    required String email,
    String? name,
    String? phone,
    String? accessToken,
    String? refreshToken,
  }) async {
    await Future.wait([
      prefs.setString(_keyUserId, userId),
      prefs.setString(_keyUserEmail, email),
      if (name != null) prefs.setString(_keyUserName, name),
      if (phone != null) prefs.setString(_keyUserPhone, phone),
      if (accessToken != null) prefs.setString(_keyAccessToken, accessToken),
      if (refreshToken != null) prefs.setString(_keyRefreshToken, refreshToken),
      setLoggedIn(true),
    ]);
  }

  /// الحصول على معرف المستخدم
  String? getUserId() {
    return prefs.getString(_keyUserId);
  }

  /// الحصول على بريد المستخدم
  String? getUserEmail() {
    return prefs.getString(_keyUserEmail);
  }

  /// الحصول على اسم المستخدم
  String? getUserName() {
    return prefs.getString(_keyUserName);
  }

  /// الحصول على رقم هاتف المستخدم
  String? getUserPhone() {
    return prefs.getString(_keyUserPhone);
  }

  /// الحصول على رمز الوصول
  String? getAccessToken() {
    return prefs.getString(_keyAccessToken);
  }

  /// الحصول على رمز التحديث
  String? getRefreshToken() {
    return prefs.getString(_keyRefreshToken);
  }

  /// تحديث اسم المستخدم
  Future<void> updateUserName(String name) async {
    await prefs.setString(_keyUserName, name);
  }

  /// تحديث رقم هاتف المستخدم
  Future<void> updateUserPhone(String phone) async {
    await prefs.setString(_keyUserPhone, phone);
  }

  // ==================== Tokens ====================

  /// حفظ الرموز المميزة
  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    await Future.wait([
      prefs.setString(_keyAccessToken, accessToken),
      if (refreshToken != null) prefs.setString(_keyRefreshToken, refreshToken),
    ]);
  }

  /// حذف الرموز المميزة
  Future<void> clearTokens() async {
    await Future.wait([
      prefs.remove(_keyAccessToken),
      prefs.remove(_keyRefreshToken),
    ]);
  }

  // ==================== Logout ====================

  /// تسجيل الخروج وحذف جميع البيانات
  Future<void> logout() async {
    await Future.wait([
      prefs.remove(_keyIsLoggedIn),
      prefs.remove(_keyUserId),
      prefs.remove(_keyUserEmail),
      prefs.remove(_keyUserName),
      prefs.remove(_keyUserPhone),
      prefs.remove(_keyAccessToken),
      prefs.remove(_keyRefreshToken),
    ]);
  }

  // ==================== Clear All ====================

  /// حذف جميع البيانات المحفوظة
  Future<void> clearAll() async {
    await prefs.clear();
  }

  // ==================== Helper Methods ====================

  /// التحقق من وجود بيانات المستخدم الأساسية
  bool hasUserData() {
    return getUserId() != null && getUserEmail() != null;
  }

  /// حفظ قيمة نصية
  Future<void> setString(String key, String value) async {
    await prefs.setString(key, value);
  }

  /// الحصول على قيمة نصية
  String? getString(String key) {
    return prefs.getString(key);
  }

  /// حذف قيمة
  Future<void> remove(String key) async {
    await prefs.remove(key);
  }

  /// طباعة جميع البيانات المحفوظة (للتطوير فقط)
  void printAllData() {
    AppLogger.debug(
      'SharedPreferences Data',
      category: LogCategory.general,
      data: {
        'isFirstTime': prefs.getBool(_keyIsFirstTime),
        'isLoggedIn': prefs.getBool(_keyIsLoggedIn),
        'userId': prefs.getString(_keyUserId),
        'userEmail': prefs.getString(_keyUserEmail),
        'userName': prefs.getString(_keyUserName),
        'userPhone': prefs.getString(_keyUserPhone),
        'accessToken': prefs.getString(_keyAccessToken)?.substring(0, 20),
      },
    );
  }
}
