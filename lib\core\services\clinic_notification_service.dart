import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import '../../data/models/appointment_model.dart';
import 'google_fcm_service.dart';

/// خدمة إرسال الإشعارات للعيادة (النسخة النهائية المُصلحة)
class ClinicNotificationService {
  static final ClinicNotificationService _instance =
      ClinicNotificationService._internal();
  factory ClinicNotificationService() => _instance;
  ClinicNotificationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final GoogleFCMService _fcmService = GoogleFCMService();

  /// إرسال إشعار إلغاء موعد للعيادة
  Future<bool> sendAppointmentCancellationNotification({
    required AppointmentModel appointment,
    required String patientName,
    String? cancellationReason,
  }) async {
    try {
      AppLogger.info(
        '📤 Sending appointment cancellation notification',
        category: LogCategory.notification,
        data: {'appointmentId': appointment.id, 'patientName': patientName},
      );

      // الحصول على tokens الأخصائي المحدد والإدارة
      final clinicTokens = await _getSpecificEmployeeAndAdminTokens(
        employeeId: appointment.employeeId,
        excludeCurrentUser: true,
      );

      if (clinicTokens.isEmpty) {
        AppLogger.warning(
          '⚠️ No clinic staff tokens found',
          category: LogCategory.notification,
        );
        return false;
      }

      // إعداد بيانات الإشعار
      final notificationData = <String, String>{
        'type': 'appointment_cancelled',
        'appointmentId': appointment.id,
        'patientId': appointment.patientId ?? '',
        'patientName': patientName,
        'appointmentDate': appointment.appointmentDate.toIso8601String(),
        'timeSlotId': appointment.timeSlotId?.toString() ?? '',
        'cancellationReason': cancellationReason ?? 'لم يتم تحديد السبب',
        'timestamp': DateTime.now().toIso8601String(),
      };

      final title = 'إلغاء موعد';
      final body =
          'قام المريض $patientName بإلغاء موعده في ${appointment.displayDate}';

      // إرسال الإشعار لجميع موظفي العيادة
      final results = await _fcmService.sendNotificationToMultipleTokens(
        fcmTokens: clinicTokens,
        title: title,
        body: body,
        data: notificationData,
      );

      final successCount = results.values.where((success) => success).length;
      final allSent = successCount == clinicTokens.length;

      // حفظ الإشعار في قاعدة البيانات لكل مستلم
      await _saveNotificationForRecipients(
        type: 'appointment_cancelled',
        title: title,
        body: body,
        data: notificationData,
        recipientTokens: clinicTokens,
        relatedId: appointment.id,
      );

      AppLogger.info(
        '✅ Appointment cancellation notification sent',
        category: LogCategory.notification,
        data: {
          'success': allSent,
          'tokensCount': clinicTokens.length,
          'successCount': successCount,
        },
      );

      return allSent;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to send appointment cancellation notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// إرسال إشعار تعديل موعد للعيادة
  Future<bool> sendAppointmentRescheduleNotification({
    required AppointmentModel oldAppointment,
    required AppointmentModel newAppointment,
    required String patientName,
    String? rescheduleReason,
  }) async {
    try {
      AppLogger.info(
        '📤 Sending appointment reschedule notification',
        category: LogCategory.notification,
        data: {
          'oldAppointmentId': oldAppointment.id,
          'newAppointmentId': newAppointment.id,
          'patientName': patientName,
        },
      );

      // الحصول على tokens الأخصائي المحدد والإدارة
      final clinicTokens = await _getSpecificEmployeeAndAdminTokens(
        employeeId: newAppointment.employeeId,
        excludeCurrentUser: true,
      );

      if (clinicTokens.isEmpty) {
        AppLogger.warning(
          '⚠️ No clinic staff tokens found',
          category: LogCategory.notification,
        );
        return false;
      }

      // إعداد بيانات الإشعار
      final notificationData = <String, String>{
        'type': 'appointment_rescheduled',
        'oldAppointmentId': oldAppointment.id,
        'newAppointmentId': newAppointment.id,
        'patientId': newAppointment.patientId ?? '',
        'patientName': patientName,
        'oldDate': oldAppointment.appointmentDate.toIso8601String(),
        'newDate': newAppointment.appointmentDate.toIso8601String(),
        'oldTimeSlotId': oldAppointment.timeSlotId?.toString() ?? '',
        'newTimeSlotId': newAppointment.timeSlotId?.toString() ?? '',
        'rescheduleReason': rescheduleReason ?? 'لم يتم تحديد السبب',
        'timestamp': DateTime.now().toIso8601String(),
      };

      final title = 'تعديل موعد';
      final body =
          'قام المريض $patientName بتعديل موعده من ${oldAppointment.displayDate} إلى ${newAppointment.displayDate}';

      // إرسال الإشعار لجميع موظفي العيادة
      final results = await _fcmService.sendNotificationToMultipleTokens(
        fcmTokens: clinicTokens,
        title: title,
        body: body,
        data: notificationData,
      );

      final successCount = results.values.where((success) => success).length;
      final allSent = successCount == clinicTokens.length;

      // حفظ الإشعار في قاعدة البيانات لكل مستلم
      await _saveNotificationForRecipients(
        type: 'appointment_rescheduled',
        title: title,
        body: body,
        data: notificationData,
        recipientTokens: clinicTokens,
        relatedId: newAppointment.id,
      );

      AppLogger.info(
        '✅ Appointment reschedule notification sent',
        category: LogCategory.notification,
        data: {
          'success': allSent,
          'tokensCount': clinicTokens.length,
          'successCount': successCount,
        },
      );

      return allSent;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to send appointment reschedule notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// الحصول على FCM tokens للأخصائي المحدد والإدارة
  Future<List<String>> _getSpecificEmployeeAndAdminTokens({
    String? employeeId,
    bool excludeCurrentUser = false,
  }) async {
    try {
      final tokens = <String>[];

      // الحصول على token الأخصائي المحدد
      if (employeeId != null) {
        try {
          final employeeTokenResponse = await _supabase
              .from('user_fcm_tokens')
              .select('fcm_token')
              .eq('user_id', employeeId)
              .eq('is_active', true);

          for (final item in employeeTokenResponse) {
            if (item['fcm_token'] != null &&
                item['fcm_token'].toString().isNotEmpty) {
              tokens.add(item['fcm_token'] as String);
            }
          }

          AppLogger.info(
            '👨‍⚕️ Retrieved specific employee tokens',
            category: LogCategory.notification,
            data: {
              'employeeId': employeeId,
              'tokensCount': employeeTokenResponse.length,
            },
          );
        } catch (e) {
          AppLogger.warning(
            '⚠️ Could not get employee tokens',
            category: LogCategory.notification,
            data: {'employeeId': employeeId},
          );
        }
      }

      // الحصول على tokens الإدارة والاستقبال
      try {
        var adminQuery = _supabase
            .from('user_fcm_tokens')
            .select('fcm_token, user_id')
            .eq('is_active', true);

        // استبعاد المستخدم الحالي إذا طُلب ذلك
        if (excludeCurrentUser && _supabase.auth.currentUser != null) {
          adminQuery = adminQuery.neq(
            'user_id',
            _supabase.auth.currentUser!.id,
          );
        }

        final adminTokenResponse = await adminQuery;

        // فلترة tokens الإدارة والاستقبال فقط
        for (final item in adminTokenResponse) {
          final userId = item['user_id'] as String?;
          if (userId != null && userId != employeeId) {
            // التحقق من أن المستخدم إدارة أو استقبال
            try {
              final userRole =
                  await _supabase
                      .from('admins')
                      .select('role')
                      .eq('id', userId)
                      .single();

              final role = userRole['role'] as String?;
              if (role == 'admin' || role == 'reception') {
                if (item['fcm_token'] != null &&
                    item['fcm_token'].toString().isNotEmpty) {
                  tokens.add(item['fcm_token'] as String);
                }
              }
            } catch (e) {
              // تجاهل إذا لم يكن في جدول admins
            }
          }
        }

        AppLogger.info(
          '👥 Retrieved admin and reception tokens',
          category: LogCategory.notification,
          data: {'adminTokensCount': adminTokenResponse.length},
        );
      } catch (e) {
        AppLogger.warning(
          '⚠️ Could not get admin tokens',
          category: LogCategory.notification,
        );
      }

      // إزالة التكرارات
      final uniqueTokens = tokens.toSet().toList();

      AppLogger.info(
        '🔍 Retrieved specific employee and admin tokens',
        category: LogCategory.notification,
        data: {
          'totalTokens': uniqueTokens.length,
          'employeeId': employeeId,
          'excludedCurrentUser': excludeCurrentUser,
        },
      );

      return uniqueTokens;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get specific employee and admin tokens',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// الحصول على FCM tokens لموظفي العيادة (مع إمكانية استبعاد المستخدم الحالي)
  Future<List<String>> _getClinicStaffTokens({
    bool excludeCurrentUser = false,
  }) async {
    try {
      var query = _supabase
          .from('user_fcm_tokens')
          .select('fcm_token, user_id')
          .eq('is_active', true);

      // استبعاد المستخدم الحالي إذا طُلب ذلك
      if (excludeCurrentUser && _supabase.auth.currentUser != null) {
        query = query.neq('user_id', _supabase.auth.currentUser!.id);
      }

      final response = await query;

      final tokens = <String>[];
      for (final item in response) {
        if (item['fcm_token'] != null &&
            item['fcm_token'].toString().isNotEmpty) {
          tokens.add(item['fcm_token'] as String);
        }
      }

      AppLogger.info(
        '🔍 Retrieved clinic staff tokens',
        category: LogCategory.notification,
        data: {
          'tokensCount': tokens.length,
          'excludedCurrentUser': excludeCurrentUser,
        },
      );

      return tokens;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get clinic staff tokens',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// حفظ الإشعار لكل مستلم منفصل مع related_id صحيح
  Future<void> _saveNotificationForRecipients({
    required String type,
    required String title,
    required String body,
    required Map<String, String> data,
    required List<String> recipientTokens,
    required String relatedId,
  }) async {
    try {
      // الحصول على user_ids من FCM tokens
      final recipientUserIds = await _getUserIdsFromTokens(recipientTokens);

      AppLogger.info(
        '💾 Saving notifications for recipients',
        category: LogCategory.notification,
        data: {
          'recipientCount': recipientUserIds.length,
          'relatedId': relatedId,
          'type': type,
        },
      );

      // حفظ إشعار منفصل لكل مستلم
      for (final userId in recipientUserIds) {
        try {
          await _supabase.from('notifications_log').insert({
            'user_id': userId,
            'type': type,
            'title': title,
            'message': body,
            'related_id': relatedId,
            'is_read': false,
            'sent_at': DateTime.now().toUtc().toIso8601String(),
            'created_at': DateTime.now().toUtc().toIso8601String(),
          });

          AppLogger.debug(
            '✅ Notification saved for user: $userId',
            category: LogCategory.notification,
          );
        } catch (e) {
          AppLogger.warning(
            '⚠️ Failed to save notification for user: $userId',
            category: LogCategory.notification,
            data: {'error': e.toString()},
          );
        }
      }

      AppLogger.info(
        '✅ Notifications saved for all recipients',
        category: LogCategory.notification,
        data: {'savedCount': recipientUserIds.length},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save notifications for recipients',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على user_ids من FCM tokens
  Future<List<String>> _getUserIdsFromTokens(List<String> tokens) async {
    try {
      final userIds = <String>[];

      for (final token in tokens) {
        try {
          final response = await _supabase
              .from('user_fcm_tokens')
              .select('user_id')
              .eq('fcm_token', token)
              .eq('is_active', true)
              .limit(1);

          if (response.isNotEmpty) {
            final userId = response.first['user_id'] as String?;
            if (userId != null && !userIds.contains(userId)) {
              userIds.add(userId);
            }
          }
        } catch (e) {
          AppLogger.debug(
            'Could not get user_id for token',
            category: LogCategory.notification,
          );
        }
      }

      return userIds;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get user IDs from tokens',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// حفظ الإشعار في قاعدة البيانات (مُصلح لـ user_id المطلوب) - DEPRECATED
  Future<void> _saveNotificationToDatabase({
    required String type,
    required String title,
    required String body,
    required Map<String, String> data,
    required String recipientType,
  }) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      // محاولات مختلفة مع user_id مطلوب
      final attempts = [
        // محاولة 1: بيانات كاملة مع user_id
        {
          'user_id': currentUserId,
          'type': type,
          'title': title,
          'message': body,
          'data': data,
        },
        // محاولة 2: بيانات أساسية مع user_id
        {
          'user_id': currentUserId,
          'type': type,
          'title': title,
          'message': body,
        },
        // محاولة 3: أقل البيانات مع user_id
        {'user_id': currentUserId, 'type': type, 'title': title},
        // محاولة 4: مع user_id ثابت إذا لم يكن هناك مستخدم حالي
        {
          'user_id': currentUserId ?? '1a0899da-aff2-400e-b3aa-3737ee00f88d',
          'type': type,
          'title': title,
        },
      ];

      bool saved = false;
      for (int i = 0; i < attempts.length && !saved; i++) {
        try {
          final response =
              await _supabase
                  .from('notifications_log')
                  .insert(attempts[i])
                  .select();

          AppLogger.info(
            '💾 Notification saved to database successfully (attempt ${i + 1})',
            category: LogCategory.notification,
            data: {'id': response.first['id']?.toString()},
          );
          saved = true;
        } catch (e) {
          AppLogger.debug(
            'Save attempt ${i + 1} failed: $e',
            category: LogCategory.notification,
          );
        }
      }

      if (!saved) {
        AppLogger.warning(
          '⚠️ Could not save notification to database - all attempts failed',
          category: LogCategory.notification,
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save notification to database',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إرسال إشعار عام للعيادة
  Future<bool> sendGeneralNotificationToClinic({
    required String title,
    required String body,
    required String type,
    Map<String, String>? additionalData,
  }) async {
    try {
      final clinicTokens = await _getClinicStaffTokens(
        excludeCurrentUser: true,
      );

      if (clinicTokens.isEmpty) {
        return false;
      }

      final notificationData = <String, String>{
        'type': type,
        'timestamp': DateTime.now().toIso8601String(),
        'patientId': _supabase.auth.currentUser?.id ?? '',
        if (additionalData != null) ...additionalData,
      };

      final results = await _fcmService.sendNotificationToMultipleTokens(
        fcmTokens: clinicTokens,
        title: title,
        body: body,
        data: notificationData,
      );

      final successCount = results.values.where((success) => success).length;
      final allSent = successCount == clinicTokens.length;

      // حفظ الإشعار في قاعدة البيانات لكل مستلم
      await _saveNotificationForRecipients(
        type: type,
        title: title,
        body: body,
        data: notificationData,
        recipientTokens: clinicTokens,
        relatedId: notificationData['relatedId'] ?? '',
      );

      return allSent;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to send general notification to clinic',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// إرسال إشعار لموضوع معين
  Future<bool> sendNotificationToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      return await _fcmService.sendNotificationToTopic(
        topic: topic,
        title: title,
        body: body,
        data: data,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to send topic notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }
}
