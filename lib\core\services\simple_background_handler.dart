import 'package:firebase_messaging/firebase_messaging.dart';
import '../utils/app_logger.dart';

/// معالج الرسائل في الخلفية المبسط - FCM v1 فقط
@pragma('vm:entry-point')
Future<void> simpleFirebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    AppLogger.info(
      '🔔 Background message received',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // FCM v1 يتولى عرض الإشعارات تلقائياً في الخلفية
    // لا نحتاج لعرض إشعارات محلية إضافية
  } catch (e) {
    AppLogger.error(
      '❌ Error in background handler',
      category: LogCategory.fcm,
      error: e,
    );
  }
}
