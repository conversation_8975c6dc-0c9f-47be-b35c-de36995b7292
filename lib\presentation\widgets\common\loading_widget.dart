import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';

/// ويدجت التحميل
class LoadingWidget extends StatelessWidget {
  final String? message;
  final Color? color;
  final double? size;

  const LoadingWidget({super.key, this.message, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // استخدام CustomLoading بدلاً من CircularProgressIndicator
          SizedBox(
            width: size ?? 50.w,
            height: size ?? 50.w,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // الدائرة الخارجية المتحركة
                SizedBox(
                  width: size ?? 50.w,
                  height: size ?? 50.w,
                  child: CircularProgressIndicator(
                    color: color ?? AppColors.primary,
                    strokeWidth: 3,
                  ),
                ),
                // صورة التطبيق في المنتصف
                Container(
                  width: 30, // مقاس ثابت
                  height: 30, // مقاس ثابت
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 3,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      'assets/images/logo.jpeg',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.health_and_safety_rounded,
                          color: color ?? AppColors.primary,
                          size: 15, // مقاس ثابت
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (message != null) ...[
            SizedBox(height: 16.h),
            Text(
              message!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// ويدجت تحميل صغير
class SmallLoadingWidget extends StatelessWidget {
  final Color? color;

  const SmallLoadingWidget({super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      width: 20, // مقاس ثابت
      height: 20, // مقاس ثابت
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primary, strokeWidth: 2),
          SizedBox(
            width: 12, // مقاس ثابت
            height: 12, // مقاس ثابت
            child: CircleAvatar(
              radius: 6, // مقاس ثابت
              backgroundImage: AssetImage('assets/images/logo.jpeg'),
              backgroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

/// ويدجت تحميل كبير
class LargeLoadingWidget extends StatelessWidget {
  final String? message;
  final Color? color;

  const LargeLoadingWidget({super.key, this.message, this.color});

  @override
  Widget build(BuildContext context) {
    return LoadingWidget(
      message: message ?? AppStrings.loading,
      color: color,
      size: 80.w,
    );
  }
}

/// ويدجت تحميل للصفحة كاملة
class FullPageLoadingWidget extends StatelessWidget {
  final String? message;

  const FullPageLoadingWidget({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: LoadingWidget(message: message ?? AppStrings.loading),
    );
  }
}

/// ويدجت تحميل شفاف
class OverlayLoadingWidget extends StatelessWidget {
  final String? message;
  final bool isVisible;

  const OverlayLoadingWidget({super.key, this.message, this.isVisible = true});

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        color: Colors.black.withValues(alpha: 0.5),
        child: LoadingWidget(
          message: message ?? AppStrings.loading,
          color: AppColors.white,
        ),
      ),
    );
  }
}

/// ويدجت تحميل للقائمة
class ListLoadingWidget extends StatelessWidget {
  final int itemCount;

  const ListLoadingWidget({super.key, this.itemCount = 5});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      padding: EdgeInsets.all(16.w),
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: 16.h),
          child: const ShimmerLoadingItem(),
        );
      },
    );
  }
}

/// عنصر تحميل بتأثير Shimmer
class ShimmerLoadingItem extends StatelessWidget {
  final double? height;
  final double? width;
  final BorderRadius? borderRadius;

  const ShimmerLoadingItem({
    super.key,
    this.height,
    this.width,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 80.h,
      width: width ?? double.infinity,
      decoration: BoxDecoration(
        color: AppColors.shimmerBase,
        borderRadius: borderRadius ?? BorderRadius.circular(12.r),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.shimmerHighlight,
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
        ),
      ),
    );
  }
}

/// ويدجت تحميل للشبكة - محسن للاستجابة
class GridLoadingWidget extends StatelessWidget {
  final int itemCount;
  final int? crossAxisCount;

  const GridLoadingWidget({super.key, this.itemCount = 6, this.crossAxisCount});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // حساب عدد الأعمدة بناءً على عرض الشاشة
          int calculatedCrossAxisCount = crossAxisCount ?? 2;
          if (crossAxisCount == null) {
            if (constraints.maxWidth > 600) {
              calculatedCrossAxisCount = 3;
            } else if (constraints.maxWidth > 900) {
              calculatedCrossAxisCount = 4;
            }
          }

          // حساب النسبة المناسبة
          final itemWidth =
              (constraints.maxWidth - ((calculatedCrossAxisCount - 1) * 12.w)) /
              calculatedCrossAxisCount;
          final itemHeight =
              itemWidth * 1.35; // نفس النسبة المستخدمة في صفحة المنتجات
          final aspectRatio = itemWidth / itemHeight;

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: calculatedCrossAxisCount,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: aspectRatio,
            ),
            itemCount: itemCount,
            itemBuilder: (context, index) {
              return const ShimmerLoadingItem();
            },
          );
        },
      ),
    );
  }
}

/// ويدجت تحميل للبطاقة
class CardLoadingWidget extends StatelessWidget {
  const CardLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(16.w),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerLoadingItem(height: 20.h, width: 150.w),
            SizedBox(height: 8.h),
            ShimmerLoadingItem(height: 16.h, width: 100.w),
            SizedBox(height: 16.h),
            const ShimmerLoadingItem(height: 40),
          ],
        ),
      ),
    );
  }
}

/// ويدجت تحميل دائري صغير
class CircularLoadingWidget extends StatelessWidget {
  final Color? color;
  final double? size;

  const CircularLoadingWidget({super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size ?? 20.w,
      height: size ?? 20.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
          Container(
            width: (size ?? 20.w) * 0.6,
            height: (size ?? 20.h) * 0.6,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 1,
                ),
              ],
            ),
            child: ClipOval(
              child: Image.asset(
                'assets/images/logo.jpeg',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.health_and_safety_rounded,
                    color: color ?? AppColors.primary,
                    size: (size ?? 20.w) * 0.3,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
