import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'fcm_token_service.dart';
import 'simple_background_handler.dart';

/// خدمة FCM مبسطة - تستخدم FCM v1 فقط بدون إشعارات محلية
class SimpleFCMService {
  static final SimpleFCMService _instance = SimpleFCMService._internal();
  factory SimpleFCMService() => _instance;
  SimpleFCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final SupabaseClient _supabase = Supabase.instance.client;

  String? _fcmToken;
  bool _isInitialized = false;

  /// تهيئة خدمة FCM
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🚀 Initializing Simple FCM service',
        category: LogCategory.fcm,
      );

      // طلب الأذونات
      await _requestPermissions();

      // الحصول على FCM Token
      await _getFCMToken();

      // إعداد معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(
        simpleFirebaseMessagingBackgroundHandler,
      );

      // إعداد معالجات الرسائل
      _setupMessageHandlers();

      _isInitialized = true;

      AppLogger.info(
        '✅ Simple FCM service initialized successfully',
        category: LogCategory.fcm,
        data: {'fcmToken': _fcmToken ?? 'No token'},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize Simple FCM service',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// طلب أذونات FCM فقط
  Future<void> _requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: false,
        carPlay: false,
        announcement: false,
      );

      AppLogger.info(
        '✅ FCM permissions granted',
        category: LogCategory.fcm,
        data: {'authorizationStatus': settings.authorizationStatus.toString()},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request FCM permissions',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();

      AppLogger.info(
        '🔑 FCM token obtained',
        category: LogCategory.fcm,
        data: {'token': _fcmToken?.substring(0, 30) ?? 'No token'},
      );

      if (_fcmToken != null) {
        await _saveTokenToServer(_fcmToken!);
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get FCM token',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// إعداد معالجات الرسائل
  void _setupMessageHandlers() {
    // معالج الرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج النقر على الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // التحقق من الرسائل عند فتح التطبيق
    _handleInitialMessage();
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    AppLogger.info(
      '📱 Foreground message received',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // FCM v1 يتولى عرض الإشعارات تلقائياً
    // لا نحتاج لعرض إشعارات محلية إضافية
  }

  /// معالج النقر على الإشعار
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    AppLogger.info(
      '👆 Notification tapped',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'data': message.data.toString(),
      },
    );

    // يمكن إضافة منطق التنقل هنا
  }

  /// التحقق من الرسائل عند فتح التطبيق
  Future<void> _handleInitialMessage() async {
    try {
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        AppLogger.info(
          '🚀 Initial message found',
          category: LogCategory.fcm,
          data: {
            'messageId': initialMessage.messageId ?? 'No ID',
            'data': initialMessage.data.toString(),
          },
        );
        await _handleNotificationTap(initialMessage);
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error handling initial message',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// حفظ التوكن في الخادم
  Future<void> _saveTokenToServer(String token) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        final fcmTokenService = FCMTokenService();
        await fcmTokenService.saveToken(currentUser.id, token);

        AppLogger.info(
          '📤 Token saved to server',
          category: LogCategory.fcm,
          data: {'userId': currentUser.id},
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save token to server',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// معالجة تسجيل دخول المستخدم
  Future<void> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        '👤 User logged in - updating FCM token',
        category: LogCategory.fcm,
        data: {'userId': userId},
      );

      if (_fcmToken != null) {
        await _saveTokenToServer(_fcmToken!);
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user login',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// معالجة تسجيل خروج المستخدم
  Future<void> onUserLogout() async {
    try {
      AppLogger.info(
        '👋 User logged out - cleaning up FCM',
        category: LogCategory.fcm,
      );

      // يمكن إضافة منطق تنظيف إضافي هنا
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user logout',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
