import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../core/services/clinic_notification_service.dart';
import '../../../../data/models/appointment_model.dart';
import '../../../../data/repositories/appointment_repository.dart';
import 'reschedule_dialog.dart';

/// تاب مواعيدي - مع حل جذري للشاشة السوداء
class MyAppointmentsTab extends StatefulWidget {
  final String patientId;

  const MyAppointmentsTab({super.key, required this.patientId});

  @override
  State<MyAppointmentsTab> createState() => _MyAppointmentsTabState();
}

enum AppointmentFilter { all, today, upcoming, past, completed }

class _MyAppointmentsTabState extends State<MyAppointmentsTab> {
  final AppointmentRepository _appointmentRepository = AppointmentRepository();
  final SupabaseClient _supabase = Supabase.instance.client;
  final ClinicNotificationService _notificationService =
      ClinicNotificationService();

  bool _isLoading = true;
  List<AppointmentModel> _appointments = [];
  final Map<String, Map<String, dynamic>> _timeSlots = {};
  final Map<String, Map<String, dynamic>> _employees = {};
  final Map<String, List<AppointmentModel>> _groupedAppointments = {};
  AppointmentFilter _selectedFilter = AppointmentFilter.all;

  // متغير لتتبع dialog التحميل
  bool _isLoadingDialogShown = false;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  /// دالة آمنة لإغلاق dialogs مع تتبع أفضل
  void _safePopDialog() {
    try {
      if (mounted && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
        AppLogger.info(
          '✅ Dialog closed successfully',
          category: LogCategory.ui,
        );
      } else {
        AppLogger.warning(
          '⚠️ Cannot close dialog - widget not mounted or no dialog to close',
          category: LogCategory.ui,
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error closing dialog',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  /// دالة آمنة لإغلاق dialog التحميل
  void _safeCloseLoadingDialog() {
    if (_isLoadingDialogShown) {
      _safePopDialog();
      _isLoadingDialogShown = false;
      AppLogger.info('🔄 Loading dialog closed', category: LogCategory.ui);
    }
  }

  /// دالة آمنة لعرض dialog التحميل
  void _safeShowLoadingDialog(String message) {
    if (mounted && !_isLoadingDialogShown) {
      _isLoadingDialogShown = true;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(color: AppColors.primary),
                  SizedBox(width: 16.w),
                  Expanded(child: Text(message)),
                ],
              ),
            ),
      );
      AppLogger.info('📤 Loading dialog shown', category: LogCategory.ui);
    }
  }

  /// الحصول على اسم المريض
  Future<String> _getPatientName() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return 'مريض غير معروف';

      // محاولة الحصول على الاسم من جدول patients أولاً
      try {
        final response =
            await _supabase
                .from('patients')
                .select('name')
                .eq('id', currentUser.id)
                .single();

        return response['name'] ?? 'مريض غير معروف';
      } catch (e) {
        // إذا فشل، جرب جدول users
        try {
          final response =
              await _supabase
                  .from('users')
                  .select('name, full_name')
                  .eq('id', currentUser.id)
                  .single();

          return response['name'] ?? response['full_name'] ?? 'مريض غير معروف';
        } catch (e2) {
          // إذا فشل أيضاً، استخدم البيانات من Auth
          final userMetadata = currentUser.userMetadata;
          return userMetadata?['name'] ??
              userMetadata?['full_name'] ??
              currentUser.email?.split('@').first ??
              'مريض غير معروف';
        }
      }
    } catch (e) {
      AppLogger.error('Failed to get patient name', error: e);
      return 'مريض غير معروف';
    }
  }

  /// تعديل الموعد مع إرسال إشعار للعيادة (الحل الجذري للشاشة السوداء)
  Future<void> _rescheduleAppointment(AppointmentModel appointment) async {
    AppLogger.info('🔄 Starting reschedule process', category: LogCategory.ui);

    // حفظ بيانات الموعد القديم
    final oldAppointment = appointment;

    await showDialog(
      context: context,
      builder:
          (context) => RescheduleAppointmentDialog(
            appointment: appointment,
            onReschedule: () async {
              AppLogger.info(
                '📝 Reschedule confirmed by user',
                category: LogCategory.ui,
              );

              // إغلاق dialog التعديل أولاً
              _safePopDialog();

              // انتظار للتأكد من إغلاق dialog
              await Future.delayed(const Duration(milliseconds: 300));

              // تشغيل العملية في background بدون dialog تحميل
              _processRescheduleInBackground(oldAppointment, appointment);
            },
          ),
    );
  }

  /// معالجة تعديل الموعد في background بدون dialog تحميل
  Future<void> _processRescheduleInBackground(
    AppointmentModel oldAppointment,
    AppointmentModel appointment,
  ) async {
    try {
      AppLogger.info(
        '🔍 Processing reschedule in background',
        category: LogCategory.ui,
      );

      // عرض رسالة فورية للمستخدم
      if (mounted) {
        _showSuccessSnackBar('جاري معالجة تعديل الموعد...');
      }

      // الحصول على بيانات الموع�� الجديد
      final newAppointmentResponse =
          await _supabase
              .from('appointments')
              .select()
              .eq('id', appointment.id)
              .single();

      final newAppointment = AppointmentModel.fromJson(newAppointmentResponse);

      AppLogger.info(
        '📧 Sending notification to clinic in background',
        category: LogCategory.ui,
      );

      // إرسال إشعار للعيادة في background
      bool notificationSent = false;
      try {
        final patientName = await _getPatientName();
        notificationSent = await _notificationService
            .sendAppointmentRescheduleNotification(
              oldAppointment: oldAppointment,
              newAppointment: newAppointment,
              patientName: patientName,
              rescheduleReason: 'تم التعديل من قبل المريض',
            );

        AppLogger.info(
          '📤 Reschedule notification sent to clinic',
          category: LogCategory.notification,
          data: {'success': notificationSent.toString()},
        );
      } catch (notificationError) {
        AppLogger.error(
          '❌ Failed to send reschedule notification to clinic',
          category: LogCategory.notification,
          error: notificationError,
        );
      }

      AppLogger.info(
        '📊 Refreshing appointments list in background',
        category: LogCategory.ui,
      );

      // تحديث قائمة المواعيد
      if (mounted) {
        await _loadAppointments();

        // عرض رسالة النجاح النهائية
        final message =
            notificationSent
                ? 'تم تغيير موعد الحجز بنجاح وإرسال إشعار للعيادة'
                : 'تم تغيير موعد الحجز بنجاح';
        _showSuccessSnackBar(message);

        AppLogger.info(
          '✅ Reschedule process completed successfully',
          category: LogCategory.ui,
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error in background reschedule process',
        category: LogCategory.notification,
        error: e,
      );

      // تحديث قائمة المواعيد حتى في حالة الخطأ
      if (mounted) {
        await _loadAppointments();
        _showErrorSnackBar('تم تعديل الموعد ولكن حدث خطأ في إرسال الإشعار');
      }
    }
  }

  /// إلغاء الموعد مع إرسال إشعار للعيادة
  Future<void> _cancelAppointment(AppointmentModel appointment) async {
    final confirmed = await _showConfirmDialog(
      title: 'إلغاء الموعد',
      content:
          'هل أنت متأكد من إلغاء هذا الموعد؟\n\nالموعد: ${appointment.displayDate} - ${_getTimeSlotDisplay(appointment.timeSlotId)}\n\nسيتم إرسال إشعار للعيادة بالإلغاء.',
      confirmText: 'إلغاء الموعد',
      cancelText: 'تراجع',
    );

    if (confirmed == true) {
      // عرض مؤشر التحميل
      _safeShowLoadingDialog('جاري إلغاء الموعد وإرسال الإشعار...');

      try {
        AppLogger.info(
          '🗑️ Cancelling appointment',
          category: LogCategory.ui,
          data: {'appointmentId': appointment.id},
        );

        // تغيير حالة الحجز إلى ملغي
        await _supabase
            .from('appointments')
            .update({'status': 'cancelled'})
            .eq('id', appointment.id);

        // إرسال إشعار للعيادة
        try {
          final patientName = await _getPatientName();
          final notificationSent = await _notificationService
              .sendAppointmentCancellationNotification(
                appointment: appointment,
                patientName: patientName,
                cancellationReason: 'تم الإلغاء من قبل المريض',
              );

          AppLogger.info(
            '📤 Notification sent to clinic',
            category: LogCategory.notification,
            data: {'success': notificationSent.toString()},
          );
        } catch (notificationError) {
          AppLogger.error(
            '❌ Failed to send notification to clinic',
            category: LogCategory.notification,
            error: notificationError,
          );
        }

        // إخفاء مؤشر التحميل
        _safeCloseLoadingDialog();

        // تحديث قائمة المواعيد
        if (mounted) {
          await _loadAppointments();
          _showSuccessSnackBar('تم إلغاء الموعد بنجاح وإرسال إشعار للعيادة');
        }
      } catch (e) {
        // إخفاء مؤشر التحميل
        _safeCloseLoadingDialog();

        AppLogger.error(
          '❌ Error cancelling appointment',
          category: LogCategory.ui,
          error: e,
        );

        if (mounted) {
          _showErrorSnackBar('فشل في إلغاء الموعد');
        }
      }
    }
  }

  // باقي الدوال تبقى كما هي...
  Future<void> _loadAppointments() async {
    AppLogger.info(
      '🔄 Loading appointments for patient: ${widget.patientId}',
      category: LogCategory.ui,
    );

    // فحص mounted قبل setState
    if (!mounted) {
      AppLogger.warning(
        '⚠️ Widget not mounted, skipping load',
        category: LogCategory.ui,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحجوزات
      final appointments = await _appointmentRepository.getPatientAppointments(
        patientId: widget.patientId,
      );

      // ترتيب الحج��زات من القديم للأحدث
      appointments.sort(
        (a, b) => a.appointmentDate.compareTo(b.appointmentDate),
      );

      // تحميل بيانات إضافية
      await _loadTimeSlots(appointments);
      await _loadEmployees(appointments);
      _groupAppointments(appointments);

      AppLogger.info(
        '✅ Loaded ${appointments.length} appointments',
        category: LogCategory.ui,
        data: {'count': appointments.length.toString()},
      );

      // فحص mounted قبل setState
      if (mounted) {
        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error loading appointments',
        category: LogCategory.ui,
        error: e,
      );

      // فحص mounted قبل setState
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showErrorSnackBar('فشل في تحميل المواعيد');
      }
    }
  }

  Future<void> _loadTimeSlots(List<AppointmentModel> appointments) async {
    try {
      final timeSlotIds =
          appointments
              .where((a) => a.hasTimeSlot)
              .map((a) => a.timeSlotId!)
              .toSet()
              .toList();

      if (timeSlotIds.isNotEmpty) {
        // ��حميل كل slot منفرداً لتجنب مشكلة in_
        for (final slotId in timeSlotIds) {
          try {
            final response =
                await _supabase
                    .from('time_slots')
                    .select('id, start_time, end_time, employee_id')
                    .eq('id', slotId)
                    .single();

            _timeSlots[response['id']] = response;
          } catch (e) {
            // تجاهل الأخطاء للـ slots المفردة
            AppLogger.warning(
              '⚠️ Could not load time slot: $slotId',
              category: LogCategory.ui,
            );
          }
        }
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error loading time slots',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  Future<void> _loadEmployees(List<AppointmentModel> appointments) async {
    try {
      final employeeIds =
          appointments
              .where((a) => a.hasEmployee)
              .map((a) => a.employeeId!)
              .toSet()
              .toList();

      if (employeeIds.isNotEmpty) {
        // تحميل كل employee منفرداً لتجنب مشكلة in_
        for (final employeeId in employeeIds) {
          try {
            final response =
                await _supabase
                    .from('admins')
                    .select('id, name, specialization_name')
                    .eq('id', employeeId)
                    .single();

            _employees[response['id']] = response;
          } catch (e) {
            // تجاهل الأخطاء للـ employees المفردة
            AppLogger.warning(
              '⚠️ Could not load employee: $employeeId',
              category: LogCategory.ui,
            );
          }
        }
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error loading employees',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  void _groupAppointments(List<AppointmentModel> appointments) {
    _groupedAppointments.clear();

    for (final appointment in appointments) {
      if (appointment.isMultipleBooking &&
          appointment.multipleBookingGroupId != null) {
        final groupId = appointment.multipleBookingGroupId!;
        if (!_groupedAppointments.containsKey(groupId)) {
          _groupedAppointments[groupId] = [];
        }
        _groupedAppointments[groupId]!.add(appointment);
      }
    }
  }

  Map<String, dynamic> _calculateGroupTotals(String groupId) {
    final groupAppointments = _groupedAppointments[groupId] ?? [];

    double totalFee = 0;
    double totalPaid = 0;
    double totalRemaining = 0;

    for (final appointment in groupAppointments) {
      totalFee += appointment.consultationFee;
      totalPaid += appointment.paidAmount;
      totalRemaining += appointment.remainingAmount;
    }

    return {
      'totalFee': totalFee,
      'totalPaid': totalPaid,
      'totalRemaining': totalRemaining,
      'sessionCount': groupAppointments.length,
    };
  }

  String _formatTimeTo12Hour(String time24) {
    if (time24.isEmpty) return 'غير محدد';

    try {
      final parts = time24.split(':');
      if (parts.length < 2) return time24;

      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);

      String period = hour >= 12 ? 'م' : 'ص';

      if (hour == 0) {
        hour = 12;
      } else if (hour > 12) {
        hour = hour - 12;
      }

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time24;
    }
  }

  String _getTimeSlotDisplay(String? timeSlotId) {
    if (timeSlotId == null || !_timeSlots.containsKey(timeSlotId)) {
      return 'غير محدد';
    }

    final slot = _timeSlots[timeSlotId]!;
    final startTime = slot['start_time'] ?? '';
    final endTime = slot['end_time'] ?? '';

    if (startTime.isEmpty || endTime.isEmpty) {
      return 'غير محدد';
    }

    final startTime12 = _formatTimeTo12Hour(startTime);
    final endTime12 = _formatTimeTo12Hour(endTime);

    return '$startTime12 - $endTime12';
  }

  String _getEmployeeDisplay(String? employeeId) {
    if (employeeId == null || !_employees.containsKey(employeeId)) {
      return 'غير محدد';
    }

    final employee = _employees[employeeId]!;
    final name = employee['name'] ?? 'غير محدد';
    final specialization = employee['specialization_name'] ?? '';

    if (specialization.isNotEmpty) {
      return '$name ($specialization)';
    }

    return name;
  }

  String _getAppointmentStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'مؤكد';
      case 'cancelled':
        return 'ملغي';
      case 'completed':
        return 'مكتمل';
      case 'no_show':
        return 'لم يحضر';
      case 'booked':
        return 'محجوز';
      case 'pending':
        return 'في الانتظار';
      case 'rescheduled':
        return 'معاد جدولة';
      default:
        return 'غير محدد';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'booked':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
      case 'no_show':
        return AppColors.error;
      case 'pending':
      case 'rescheduled':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getArabicDayName(DateTime date) {
    const arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return arabicDays[date.weekday - 1];
  }

  List<AppointmentModel> get _filteredAppointments {
    switch (_selectedFilter) {
      case AppointmentFilter.all:
        return _appointments;
      case AppointmentFilter.today:
        return _appointments
            .where((appointment) => appointment.isToday)
            .toList();
      case AppointmentFilter.upcoming:
        return _appointments
            .where((appointment) => appointment.isFuture)
            .toList();
      case AppointmentFilter.past:
        return _appointments
            .where((appointment) => appointment.isPast)
            .toList();
      case AppointmentFilter.completed:
        return _appointments
            .where((appointment) => appointment.isCompleted)
            .toList();
    }
  }

  String _getFilterTitle(AppointmentFilter filter) {
    switch (filter) {
      case AppointmentFilter.all:
        return 'الكل';
      case AppointmentFilter.today:
        return 'اليوم';
      case AppointmentFilter.upcoming:
        return 'القادمة';
      case AppointmentFilter.past:
        return 'السابقة';
      case AppointmentFilter.completed:
        return 'المكتملة';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    return Column(
      children: [
        _buildFilterTabs(),
        _buildStatsRow(),
        Expanded(child: _buildAppointmentsList()),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 40.w,
            height: 40.h,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل المواعيد...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children:
              AppointmentFilter.values.map((filter) {
                final isSelected = _selectedFilter == filter;
                return GestureDetector(
                  onTap: () {
                    if (mounted) {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h,
                    ),
                    margin: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      _getFilterTitle(filter),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                        color:
                            isSelected
                                ? AppColors.white
                                : AppColors.textSecondary,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildStatsRow() {
    final filteredCount = _filteredAppointments.length;
    final totalCount = _appointments.length;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            'عرض $filteredCount من أصل $totalCount موعد',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsList() {
    final filteredAppointments = _filteredAppointments;

    if (filteredAppointments.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadAppointments,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredAppointments.length,
        itemBuilder: (context, index) {
          final appointment = filteredAppointments[index];
          return _buildAppointmentCard(appointment);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    String emptyMessage;
    String emptySubtitle;
    IconData emptyIcon;

    switch (_selectedFilter) {
      case AppointmentFilter.all:
        emptyMessage = 'لا توجد مواعيد';
        emptySubtitle = 'لم تقم بحجز أي مواعيد بعد';
        emptyIcon = Icons.calendar_today;
        break;
      case AppointmentFilter.today:
        emptyMessage = 'لا توجد مواعيد اليوم';
        emptySubtitle = 'لا توجد مواعيد مجدولة لهذا اليوم';
        emptyIcon = Icons.today;
        break;
      case AppointmentFilter.upcoming:
        emptyMessage = 'لا توجد مواعيد قادمة';
        emptySubtitle = 'جميع مواعيدك قد انتهت أو تم إلغاؤها';
        emptyIcon = Icons.schedule;
        break;
      case AppointmentFilter.past:
        emptyMessage = 'لا توجد مواعيد سابقة';
        emptySubtitle = 'لم تحضر أي مواعيد ساب��ة';
        emptyIcon = Icons.history;
        break;
      case AppointmentFilter.completed:
        emptyMessage = 'لا توجد مواعيد مكتملة';
        emptySubtitle = 'لم تكمل أي مواعيد بعد';
        emptyIcon = Icons.check_circle_outline;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(emptyIcon, size: 64.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            emptySubtitle,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(AppointmentModel appointment) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _getStatusColor(appointment.status).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(14.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              _buildCardHeader(appointment),
              SizedBox(height: 12.h),

              // معلومات الموعد
              _buildAppointmentInfo(appointment),

              // معلومات الحجز المتعدد
              if (appointment.isMultipleBooking) ...[
                SizedBox(height: 8.h),
                _buildMultipleBookingInfo(appointment),
              ],

              // معلومات الرسوم
              SizedBox(height: 8.h),
              _buildPaymentInfo(appointment),

              // الملاحظات
              if (appointment.hasNotes) ...[
                SizedBox(height: 8.h),
                _buildNotesSection(appointment),
              ],

              // ملاحظات الجلسة
              if (appointment.hasSessionNotes) ...[
                SizedBox(height: 8.h),
                _buildSessionNotesSection(appointment),
              ],

              // الوقت المتبقي (للمواعيد القادمة فقط)
              if (appointment.isFuture &&
                  appointment.timeUntilAppointment.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildTimeUntilSection(appointment),
              ],

              // الأزرار
              if (_canModifyAppointment(appointment)) ...[
                SizedBox(height: 12.h),
                _buildActionButtons(appointment),
              ],
            ],
          ),
        ),
      ),
    );
  }

  bool _canModifyAppointment(AppointmentModel appointment) {
    // يمكن تعديل الموعد إذا كان في المستقبل وليس مكتمل أو ملغي
    return appointment.isFuture &&
        !appointment.isCompleted &&
        appointment.status.toLowerCase() != 'cancelled';
  }

  Widget _buildCardHeader(AppointmentModel appointment) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            Icons.calendar_today,
            color: AppColors.primary,
            size: 18.sp,
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getArabicDayName(appointment.appointmentDate),
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
              ),
              Text(
                appointment.displayDate,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
        _buildStatusChip(appointment),
      ],
    );
  }

  Widget _buildAppointmentInfo(AppointmentModel appointment) {
    return Column(
      children: [
        _buildInfoRow(
          Icons.access_time,
          'الوقت',
          _getTimeSlotDisplay(appointment.timeSlotId),
        ),
        SizedBox(height: 6.h),
        _buildInfoRow(
          Icons.person,
          'الأخصائي',
          _getEmployeeDisplay(appointment.employeeId),
        ),
        SizedBox(height: 6.h),
        _buildInfoRow(Icons.timer, 'المدة', appointment.formattedDuration),
      ],
    );
  }

  Widget _buildMultipleBookingInfo(AppointmentModel appointment) {
    final bookingType =
        appointment.isMultipleBooking ? 'حجز متعدد' : 'حجز مرة واحدة';

    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.repeat, color: AppColors.info, size: 14.sp),
              SizedBox(width: 6.w),
              Text(
                'نوع الحجز: $bookingType',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.info,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          if (appointment.isMultipleBooking &&
              appointment.multipleBookingGroupId != null) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.format_list_numbered,
                  color: AppColors.info,
                  size: 14.sp,
                ),
                SizedBox(width: 6.w),
                Text(
                  'الجلسة رقم ${appointment.bookingSequence ?? 1} من ${_calculateGroupTotals(appointment.multipleBookingGroupId!)['sessionCount']} جلسات',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.info,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentInfo(AppointmentModel appointment) {
    double totalFee = appointment.consultationFee;
    double totalPaid = appointment.paidAmount;
    double totalRemaining = appointment.remainingAmount;

    // إذا كان حجز متعدد، احسب المجموع
    if (appointment.isMultipleBooking &&
        appointment.multipleBookingGroupId != null) {
      final totals = _calculateGroupTotals(appointment.multipleBookingGroupId!);
      totalFee = totals['totalFee'];
      totalPaid = totals['totalPaid'];
      totalRemaining = totals['totalRemaining'];
    }

    return Container(
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: AppColors.textSecondary, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'معلومات الرسوم',
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPaymentItem(
                'الرسوم',
                '${totalFee.toStringAsFixed(0)} د.ا',
                AppColors.textPrimary,
              ),
              _buildPaymentItem(
                'المدفوع',
                '${totalPaid.toStringAsFixed(0)} د.ا',
                AppColors.success,
              ),
              _buildPaymentItem(
                'المتبقي',
                '${totalRemaining.toStringAsFixed(0)} د.ا',
                AppColors.error,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 11.sp, color: AppColors.textSecondary),
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection(AppointmentModel appointment) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: AppColors.textSecondary, size: 14.sp),
              SizedBox(width: 6.w),
              Text(
                'ملاحظات',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            appointment.notes!,
            style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionNotesSection(AppointmentModel appointment) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assignment, color: AppColors.success, size: 14.sp),
              SizedBox(width: 6.w),
              Text(
                'ملاحظات الجلسة',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            appointment.sessionNotes!,
            style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeUntilSection(AppointmentModel appointment) {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, color: AppColors.primary, size: 14.sp),
          SizedBox(width: 6.w),
          Text(
            appointment.timeUntilAppointment,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AppointmentModel appointment) {
    return Row(
      children: [
        const Spacer(),
        if (!appointment.isMultipleBooking) ...[
          // زر إلغاء للحجز المفرد
          TextButton.icon(
            onPressed: () => _cancelAppointment(appointment),
            icon: Icon(Icons.cancel, color: AppColors.error, size: 14.sp),
            label: Text(
              'إلغاء الموعد',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 8.w),
        ],
        // زر تغيير الموعد (للجميع)
        TextButton.icon(
          onPressed: () => _rescheduleAppointment(appointment),
          icon: Icon(
            Icons.edit_calendar,
            color: AppColors.primary,
            size: 14.sp,
          ),
          label: Text(
            'تغيير الموعد',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 14.sp),
        SizedBox(width: 6.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(AppointmentModel appointment) {
    final statusColor = _getStatusColor(appointment.status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        _getAppointmentStatusText(appointment.status),
        style: TextStyle(
          fontSize: 10.sp,
          fontWeight: FontWeight.w600,
          color: statusColor,
        ),
      ),
    );
  }

  // Helper methods for dialogs and snackbars
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(confirmText),
              ),
            ],
          ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
