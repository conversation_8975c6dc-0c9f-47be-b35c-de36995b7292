class NotificationModel {
  final String id;
  final String? userId;
  final String type;
  final String title;
  final String? message;
  final String? content;
  final String? description;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;

  NotificationModel({
    required this.id,
    this.userId,
    required this.type,
    required this.title,
    this.message,
    this.content,
    this.description,
    this.data,
    required this.createdAt,
    this.isRead = false,
    this.imageUrl,
    this.actionUrl,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      userId: json['user_id'],
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      message: json['message'],
      content: json['content'],
      description: json['description'],
      data:
          json['data'] is Map ? Map<String, dynamic>.from(json['data']) : null,
      createdAt: _parseDateTime(json['created_at']),
      isRead: json['is_read'] ?? false,
      imageUrl: json['image_url'],
      actionUrl: json['action_url'],
    );
  }

  /// تحويل التاريخ من UTC إلى التوقيت المحلي
  static DateTime _parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) {
      return DateTime.now();
    }

    try {
      // تحويل من UTC إلى التوقيت المحلي
      final utcDateTime = DateTime.parse(dateTimeString);
      return utcDateTime.toLocal();
    } catch (e) {
      // في حالة فشل التحويل، استخدم الوقت الحالي
      return DateTime.now();
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type,
      'title': title,
      'message': message,
      'content': content,
      'description': description,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'image_url': imageUrl,
      'action_url': actionUrl,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? type,
    String? title,
    String? message,
    String? content,
    String? description,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    String? imageUrl,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      content: content ?? this.content,
      description: description ?? this.description,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  /// الحصول على المحتوى النصي للإشعار
  String get bodyText {
    return message ?? content ?? description ?? '';
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  String get iconPath {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
      case 'appointment_reminder':
        return 'assets/icons/calendar_check.png';
      case 'appointment_cancelled':
        return 'assets/icons/calendar_cancel.png';
      case 'appointment_rescheduled':
        return 'assets/icons/calendar_edit.png';
      case 'payment_received':
      case 'payment_reminder':
        return 'assets/icons/payment.png';
      case 'general':
      case 'announcement':
        return 'assets/icons/info.png';
      default:
        return 'assets/icons/notification.png';
    }
  }

  /// الحصول على لون الإشعار حسب النوع
  String get colorHex {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
        return '#4CAF50'; // أخضر
      case 'appointment_cancelled':
        return '#F44336'; // أحمر
      case 'appointment_rescheduled':
        return '#FF9800'; // برتقالي
      case 'payment_received':
        return '#2196F3'; // أزرق
      case 'payment_reminder':
        return '#FF5722'; // أحمر برتقالي
      case 'general':
      case 'announcement':
        return '#9C27B0'; // بنفسجي
      default:
        return '#607D8B'; // رمادي مزرق
    }
  }

  /// عرض التاريخ والوقت بنظام 12 ساعة
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    // إذا كان الإشعار من نفس اليوم، اعرض الوقت فقط
    if (createdAt.year == now.year &&
        createdAt.month == now.month &&
        createdAt.day == now.day) {
      return formattedTime;
    }

    // إذا كان من أمس
    if (difference.inDays == 1) {
      return 'أمس $formattedTime';
    }

    // إذا كان من هذا الأسبوع (آخر 7 أيام)
    if (difference.inDays < 7) {
      return '$arabicDayName $formattedTime';
    }

    // إذا كان أقدم من أسبوع، اعرض التاريخ والوقت
    return '$formattedDate $formattedTime';
  }

  /// تنسيق الوقت بنظام 12 ساعة
  String get formattedTime {
    final hour = createdAt.hour;
    final minute = createdAt.minute;

    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final period = hour >= 12 ? 'م' : 'ص';

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// تنسيق التاريخ
  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  /// اسم اليوم بالعربية
  String get arabicDayName {
    const days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return days[createdAt.weekday - 1];
  }

  /// تحويل النوع إلى نص عربي
  String get typeDisplayName {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
        return 'تأكيد موعد';
      case 'appointment_cancelled':
        return 'إلغاء موعد';
      case 'appointment_rescheduled':
        return 'تعديل موعد';
      case 'appointment_reminder':
        return 'تذكير بموعد';
      case 'payment_received':
        return 'استلام دفعة';
      case 'payment_reminder':
        return 'تذكير بدفعة';
      case 'general':
        return 'إشعار عام';
      case 'announcement':
        return 'إعلان';
      default:
        return 'إشعار';
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, type: $type, title: $title, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
